import { useState } from "react";
import PricingCard from "./pricing-card";
import { ArrowRight } from "react-feather";
import { Link } from "@tanstack/react-router";
import CheckoutPage from "./checkout";
import { ICONS } from "@/lib/assets/images";

type PlanType = "daily" | "subscription" | "custom";

type PlanInfo = {
	type: PlanType;
	price: number;
	days?: number; // For custom plans
};

const PLAN_FEATURES = {
	daily: [
		"24-hour access to all content",
		"Full access to premium features",
		"No commitment required",
		"Instant activation",
		"Cancel anytime",
	],
	subscription: [
		"Unlimited access to all content",
		"Full access to premium features",
		"Priority customer support",
		"Regular content updates",
		"Save 30% ",
	],
	custom: [
		"Lorem ipsum",
		"Lorem ipsum",
		"Lorem ipsum",
		"Lorem ipsum",
	],
};

const PricingPage = () => {
	const [showCheckout, setShowCheckout] = useState<boolean>(false);
	const [selectedPlan, setSelectedPlan] = useState<PlanInfo | null>(null);

	const handleSelectPlan = (planType: PlanType, price: number, days?: number) => {
		setSelectedPlan({ type: planType, price: price, days });
		setShowCheckout(true);
	};

	const handleBackToPricing = () => {
		setShowCheckout(false);
	};

	if (showCheckout && selectedPlan) {
		return (
			<CheckoutPage
				planType={selectedPlan.type}
				price={selectedPlan.price}
				days={selectedPlan.days}
				onBack={handleBackToPricing}
			/>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col justify-between p-6">
			<div className="relative w-full mb-12 pt-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center text-purple-600 font-bold text-2xl ml-8">
						<img
							src={ICONS.logoexpanded}
							alt="Logo"
							className="w-32 sm:w-36 lg:w-40"
						/>
					</div>

					<div className="flex-1 text-center">
						<h1 className="font-bold mb-2 text-2xl">Ready to Get Started?</h1>
						<p className="text-sm text-gray-600">
							14 days unlimited free trial. No contract or credit card required.
						</p>
					</div>

					<div className="w-24 sm:w-28 lg:w-32 mr-16" />
				</div>
			</div>

			<div className="flex flex-col md:flex-row gap-8 justify-center w-full my-8">
				<PricingCard
					title="Daily Plan"
					price={99}
					period="day"
					features={PLAN_FEATURES.daily}
					onGetPlan={() => handleSelectPlan("daily", 99)}
				/>

				<PricingCard
					title="Subscription Plan"
					price={1000}
					period="month"
					features={PLAN_FEATURES.subscription}
					isPrimary={true}
					onGetPlan={() => handleSelectPlan("subscription", 200)}
				/>

				<PricingCard
					title="Custom Plan"
					features={PLAN_FEATURES.custom}
					isCustom={true}
					onGetPlan={(days?: number, price?: number) => handleSelectPlan("custom", price || 0, days)}
				/>
			</div>

			<div className="w-full text-center mt-8 mb-12">
				<Link
					className="text-xl text-accent hover:text-purple-800 hover:underline transition-colors duration-300 inline-flex items-center font-medium"
					to="/dashboard"
				>
					Start my 14-day free trial
					<ArrowRight className="h-4 w-4 ml-1" />
				</Link>
			</div>
		</div>
	);
};

export default PricingPage;
