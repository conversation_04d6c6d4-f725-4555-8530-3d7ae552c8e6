import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios";
import {
	errorInterceptor,
	requestInterceptor,
	successInterceptor,
} from "./interceptors";

const axiosRequestConfig: AxiosRequestConfig = {
	baseURL: import.meta.env["VITE_API_URL"],
	responseType: "json",
	headers: {
		"Content-Type": "application/json",
		"Access-Control-Allow-Origin": "*",
	},
	paramsSerializer: {
		indexes: null, // Global setting: serialize arrays as param=value1&param=value2 instead of param[]=value1
	},
};

const api: AxiosInstance = axios.create(axiosRequestConfig);

api.interceptors.request.use(requestInterceptor);
api.interceptors.response.use(successInterceptor, errorInterceptor);

export { api };
