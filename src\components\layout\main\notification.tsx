"use client";

import {
	Too<PERSON><PERSON>,
	Too<PERSON><PERSON>Content,
	TooltipTrigger,
	TooltipProvider,
} from "@/components/ui/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell } from "react-feather";
import clsx from "clsx"; // Optional helper for cleaner classNames

const Notifications = ({ disabled = false }: { disabled?: boolean }) => {
	return (
		<DropdownMenu>
			<TooltipProvider disableHoverableContent>
				<Tooltip delayDuration={100}>
					<TooltipTrigger asChild>
						<DropdownMenuTrigger asChild disabled={disabled}>
							<Bell
								size={48}
								className={clsx("border rounded-full p-3", {
									"border-gray-200 text-gray-600 hover:cursor-pointer":
										!disabled,
									"border-gray-100 text-gray-400 cursor-not-allowed bg-gray-50":
										disabled,
								})}
							/>
						</DropdownMenuTrigger>
					</TooltipTrigger>
					{!disabled && (
						<TooltipContent side="bottom">Notifications</TooltipContent>
					)}
				</Tooltip>
			</TooltipProvider>

			{!disabled && (
				<DropdownMenuContent className="w-56" align="end" forceMount>
					Notifications content
				</DropdownMenuContent>
			)}
		</DropdownMenu>
	);
};

export default Notifications;
