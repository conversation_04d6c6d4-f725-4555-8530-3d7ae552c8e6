import { createFileRoute, <PERSON> } from "@tanstack/react-router";
// import PricingPage from "@/components/layout/pricing/plan";
// import { useMediaQuery } from 'react-responsive';
// import MobilePricingPage from "@/components/layout/pricing/mobile-plan";

const Page = () => {
	// Temp Adding NotFound component
	return (
		<div className="min-h-screen flex flex-col items-center justify-center bg-white">
			<div className="text-center p-8 max-w-md">
				<h1 className="text-4xl font-bold text-accent mb-4">Page Not Found</h1>
				<p className="text-[#64748B] mb-8">
					The page you are looking for doesn't exist or has been moved.
				</p>
				<Link
					to="/dashboard"
					className="bg-accent text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-colors"
				>
					Back to Home
				</Link>
			</div>
		</div>
	);
	//

	// const isDesktop = useMediaQuery({ minWidth: 768 });
	// return isDesktop ? <PricingPage /> : <MobilePricingPage />;
};

export const Route = createFileRoute("/(app)/_pricing/selectplan")({
	component: Page,
});
export default Page;
