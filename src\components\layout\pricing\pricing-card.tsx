import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON>ontent,
	<PERSON><PERSON>ooter,
	CardHeader,
} from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Check } from "react-feather";

type PricingCardProps = {
	title: string;
	price?: number; // Optional for custom plans
	period?: string; // Optional for custom plans
	features: string[];
	isPrimary?: boolean;
	isCustom?: boolean; // New prop to indicate custom plan
	onGetPlan?: (days?: number, price?: number) => void; // Updated to support custom plan params
};

const PricingCard = ({
	title,
	price,
	period,
	features,
	isPrimary = false,
	isCustom = false,
	onGetPlan = () => {},
}: PricingCardProps) => {
	// State for custom plan slider
	const [days, setDays] = useState(365);

	// Calculate price for custom plans
	const calculateCustomPrice = (days: number) => {
		return Math.round((days / 365) * 1000);
	};

	// Get the actual price and period to display
	const displayPrice = isCustom ? calculateCustomPrice(days) : price;
	const displayPeriod = isCustom ? `${days} Days` : period;

	// Handle button click
	const handleGetPlan = () => {
		if (isCustom) {
			onGetPlan(days, displayPrice);
		} else {
			onGetPlan();
		}
	};
	return (
		<Card
			className={`py-10 px-8 mb:py-14 mb:px-10 rounded-xl w-full md:w-[370px] shadow-lg ${
				isCustom
					? "text-white bg-gradient-to-b from-accent to-[#2D1B67]"
					: isPrimary
						? "bg-accent text-white"
						: "bg-white"
			} flex flex-col`}
		>
			<CardHeader className="p-0 space-y-[15px]">
				<h3 className="text-xl md:text-2xl font-bold">{title}</h3>

				{/* Slider for custom plans */}
				{isCustom && (
					<div className="pt-2.5">
						<Slider
							value={days}
							onChange={setDays}
							min={1}
							max={365}
							step={1}
							className="bg-[#D9D9D9] h-[3px] shadow-[0px_4px_4px_0px_#00000040] [&::-webkit-slider-track]:bg-[#D9D9D9] [&::-webkit-slider-track]:rounded-lg [&::-moz-range-track]:bg-[#D9D9D9] [&::-moz-range-track]:rounded-lg slider-thumb:bg-[#D9D9D9] slider-thumb:shadow-[0px_4px_4px_0px_#00000040] [&::-webkit-slider-thumb]:bg-[#D9D9D9] [&::-webkit-slider-thumb]:shadow-[0px_4px_4px_0px_#00000040] [&::-moz-range-thumb]:bg-[#D9D9D9] [&::-moz-range-thumb]:shadow-[0px_4px_4px_0px_#00000040]"
						/>
					</div>
				)}

				<div className="flex items-end gap-1 leading-[38px] md:leading-[42px]">
					<span className="text-sm md:text-xl font-bold">Rs.</span>
					<span className="text-[40px] md:text-5xl font-bold">
						{displayPrice?.toLocaleString()}
					</span>
					<span
						className={`text-sm font-semibold md:text-base ${
							isCustom || isPrimary ? "text-[#FFFFFF66]" : "text-[#656565]"
						}`}
					>
						/{displayPeriod}
					</span>
				</div>
			</CardHeader>

			<CardContent className="p-0 py-10 mb:py-12 space-y-6 flex-grow">
				{features.map((feature, index) => (
					<div key={index} className="flex items-center gap-3">
						<div className="p-1">
							<Check
								className={`h-4 w-4 ${
									isCustom || isPrimary ? "text-white" : "text-accent"
								}`}
							/>
						</div>
						<span
							className={`text-sm ${isCustom || isPrimary ? "" : "text-gray-700"}`}
						>
							{feature}
						</span>
					</div>
				))}
			</CardContent>

			<CardFooter className="p-0 mt-auto">
				<Button
					onClick={handleGetPlan}
					className={`w-full py-6 ${
						isCustom || isPrimary
							? "bg-white hover:bg-gray-100 text-black"
							: "bg-accent text-white"
					}`}
				>
					Get
				</Button>
			</CardFooter>
		</Card>
	);
};

export default PricingCard;
