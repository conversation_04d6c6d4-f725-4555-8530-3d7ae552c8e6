import { useQuery } from "@tanstack/react-query";
import { getMockTests, getAllQuizzes } from "@/features/tests/services";

export const useGetMockTests = () => {
	return useQuery({
		queryKey: ["mockTests"],
		queryFn: getMockTests,
		staleTime: 1000 * 60 * 60, // 1 hour - mock tests don't change often
		refetchOnWindowFocus: false,
		select: (data) => data.data.data,
	});
};

export const useGetAllQuizzes = (page: number = 1, limit: number = 10) => {
	return useQuery({
		queryKey: ["allQuizzes", page, limit],
		queryFn: () => getAllQuizzes(page, limit),
		refetchOnWindowFocus: false,
		select: (data) => data.data.data,
	});
};
