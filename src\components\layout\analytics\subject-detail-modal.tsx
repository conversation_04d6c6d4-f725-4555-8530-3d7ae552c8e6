import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON><PERSON> } from "@/components/charts";
import { AnalyticsCard } from './analytics-card';
import { AnalyticsSection } from './analytics-section';
import { CHART_COLORS } from './types';
import { transformSubjectData } from './utils';

interface SubjectDetailModalProps {
	isOpen: boolean;
	onClose: () => void;
	subjectName: string;
	subjectData: any;
}

export const SubjectDetailModal = ({
	isOpen,
	onClose,
	subjectName,
	subjectData,
}: SubjectDetailModalProps) => {
	if (!subjectData) return null;

	const difficultyData = transformSubjectData(subjectData.difficulty);
	const typeData = transformSubjectData(subjectData.type);

	const totalCorrect = Object.values(subjectData.difficulty || {}).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
	const totalWrong = Object.values(subjectData.difficulty || {}).reduce((sum: number, data: any) => sum + (data.wrong || 0), 0);
	const totalAttempted = totalCorrect + totalWrong;

	const totalAttemptedData = [
		{ name: "Correct Answers", value: totalCorrect, fill: CHART_COLORS.correct },
		{ name: "Wrong Answers", value: totalWrong, fill: CHART_COLORS.wrong },
	];

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="text-xl font-bold text-gray-700">
						{subjectName} - Detailed Analytics
					</DialogTitle>
				</DialogHeader>

				<AnalyticsCard
					title="TOTAL ATTEMPTED MCQ'S"
					value={totalAttempted}
					subtitle="total"
					className="mb-0"
				>
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-9 lg:justify-between">
						<PieChart
							data={totalAttemptedData}
							showLegend={true}
							innerRadius={0}
							outerRadius={70}
							className="flex flex-col justify-between"
						/>
						<AnalyticsSection title="MCQ's Type" data={typeData} borderColor="border-[#D2D5DA]" />
						<AnalyticsSection title="Difficulty Type" data={difficultyData} />
					</div>
				</AnalyticsCard>
			</DialogContent>
		</Dialog>
	);
};
