// Loading Components
export const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

export const LoadingSection = ({ title, count, borderColor = "border-gray-200" }: {
	title: string;
	count: number;
	borderColor?: string;
}) => (
	<div className="flex flex-col justify-between">
		<LoadingBlock className={`h-4 w-${title.length > 10 ? '24' : '20'} mb-4`} />
		<div className="space-y-2.5">
			{[...Array(count)].map((_, i) => (
				<div key={i} className={`flex items-center justify-between py-2.5 px-5 bg-white border ${borderColor} rounded-lg shadow-sm`}>
					<div className="flex flex-col">
						<LoadingBlock className="h-4 w-16 mb-1" />
						<div className="flex items-baseline gap-1">
							<LoadingBlock className="h-6 w-8" />
							<LoadingBlock className="h-3 w-8" />
						</div>
					</div>
					<LoadingBlock className="w-[63px] h-[63px] rounded-full" />
				</div>
			))}
		</div>
	</div>
);

export const AnalyticsMainLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
		<div className="mb-6">
			<LoadingBlock className="h-4 w-48 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-16" />
				<LoadingBlock className="h-4 w-12" />
			</div>
		</div>
		<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
			<div className="flex flex-col justify-between">
				<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
				<div className="space-y-2">
					{[...Array(2)].map((_, i) => (
						<div key={i} className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<LoadingBlock className="w-4 h-4 rounded-full" />
								<LoadingBlock className="h-4 w-24" />
							</div>
							<LoadingBlock className="h-4 w-8" />
						</div>
					))}
				</div>
			</div>
			<LoadingSection title="MCQ's Type" count={2} borderColor="border-[#D2D5DA]" />
			<LoadingSection title="Difficulty Type" count={3} />
			<LoadingSection title="Subject Wise" count={4} />
		</div>
	</div>
);

export const AnalyticsFutureLoading = ({ type }: { type: 'left' | 'right' }) => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 flex flex-col">
		<div className="mb-6">
			<LoadingBlock className={`h-4 ${type === 'left' ? 'w-32' : 'w-40'} mb-2.5`} />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className={`h-8 ${type === 'left' ? 'w-16' : 'w-12'}`} />
				<LoadingBlock className={`h-4 ${type === 'left' ? 'w-16' : 'w-20'}`} />
			</div>
		</div>
		{type === 'left' ? (
			<div className="h-full flex flex-col justify-between">
				<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
				<div className="space-y-2">
					{[...Array(2)].map((_, i) => (
						<div key={i} className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<LoadingBlock className="w-4 h-4 rounded-full" />
								<LoadingBlock className="h-4 w-24" />
							</div>
							<LoadingBlock className="h-4 w-8" />
						</div>
					))}
				</div>
			</div>
		) : (
			<div className="space-y-4">
				{[...Array(4)].map((_, i) => (
					<div key={i} className="space-y-2">
						<LoadingBlock className="h-4 w-16" />
						<LoadingBlock className="h-4 w-full" />
					</div>
				))}
			</div>
		)}
	</div>
);
