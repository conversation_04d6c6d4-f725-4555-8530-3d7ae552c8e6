import * as React from "react";
import { <PERSON>, <PERSON>Off } from "react-feather";
import { Input } from "./input";
import { cn } from "@/lib/utils";

export interface PasswordInputProps
	extends React.InputHTMLAttributes<HTMLInputElement> {
	className?: string;
}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
	({ className, ...props }, ref) => {
		const [showPassword, setShowPassword] = React.useState(false);

		const togglePasswordVisibility = () => {
			setShowPassword(!showPassword);
		};

		const EyeIcon = showPassword ? EyeOff : Eye;

		return (
			<div className="relative">
				<Input
					type={showPassword ? "text" : "password"}
					className={cn("pr-10", className)}
					ref={ref}
					{...props}
				/>
				<button
					type="button"
					onClick={togglePasswordVisibility}
					className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none transition-colors duration-200"
					title={showPassword ? "Hide password" : "Show password"}
					aria-label={showPassword ? "Hide password" : "Show password"}
				>
					<EyeIcon
						size={16}
						className="transition-all duration-200 ease-in-out"
					/>
				</button>
			</div>
		);
	}
);

PasswordInput.displayName = "PasswordInput";

export { PasswordInput };
