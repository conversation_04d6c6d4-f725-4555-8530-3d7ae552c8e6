// Analytics Types
export interface AnalyticsData {
	name: string;
	total: number;
	correct: { value: number; fill: string };
	wrong: { value: number; fill: string };
}

export interface TimeFrameData {
	mcqsSolvedCount: number;
	correctAnswers: number;
	wrongAnswers: number;
	mcqsType: Record<string, number>;
	difficultyType: Record<string, number>;
	subjects: Record<string, number>;
	subject_division: Record<string, any>;
}

export type TimeFrame = 'twentyFourHours' | 'sevenDays' | 'thirtyDays' | 'overall';

// Constants
export const CHART_COLORS = {
	correct: "rgba(89, 54, 205, 1)",
	wrong: "rgba(89, 54, 205, 0.5)",
} as const;

export const TIME_FRAME_OPTIONS = [
	{ key: 'twentyFourHours' as const, label: 'Last 24h' },
	{ key: 'sevenDays' as const, label: 'Last 7 days' },
	{ key: 'overall' as const, label: 'Overall Performance' },
] as const;
