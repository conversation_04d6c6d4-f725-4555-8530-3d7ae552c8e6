import { Input } from "@/components/ui/input";
import { useMediaQuery } from 'react-responsive';
import { Search } from "react-feather";

const SearchChapter = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });
	if (isDesktop)
		return (
			<div className="flex flex-1 gap-x-3">
				<Input
					startIcon={Search}
					placeholder="Search Here"
					className="sans h-14 rounded-[10px] placeholder:text-gray-400 text-gray-600 items-center"
				/>
			</div>
		);
	else
		return (
			<div className="lg:hidden">
				<Search size={24} />
			</div>
		);
};

export default SearchChapter;
