import { useState } from "react";
import { ArrowRight } from "react-feather";
import PricingCard from "./pricing-card";
import MobileCheckoutPage from "./mobile-checkout";
import { ICONS } from "@/lib/assets/images";
import { Link } from "@tanstack/react-router";

type PlanType = "daily" | "subscription" | "custom";

type PlanInfo = {
	type: PlanType;
	price: number;
	days?: number; // For custom plans
};

const PLAN_FEATURES = {
	daily: [
		"24-hour access to all content",
		"Full access to premium features",
		"No commitment required",
		"Instant activation",
		"Cancel anytime",
	],
	subscription: [
		"Unlimited access to all content",
		"Full access to premium features",
		"Priority customer support",
		"Regular content updates",
		"Save 30% ",
	],
	custom: ["Lorem ipsum", "Lorem ipsum", "Lorem ipsum", "Lorem ipsum"],
};

const MobilePricingPage = () => {
	const [showCheckout, setShowCheckout] = useState<boolean>(false);
	const [selectedPlan, setSelectedPlan] = useState<PlanInfo | null>(null);

	const handleSelectPlan = (
		planType: PlanType,
		price: number,
		days?: number
	) => {
		setSelectedPlan({ type: planType, price: price, days });
		setShowCheckout(true);
	};

	const handleBackToPricing = () => {
		setShowCheckout(false);
	};

	if (showCheckout && selectedPlan) {
		return (
			<MobileCheckoutPage
				planType={selectedPlan.type}
				price={selectedPlan.price}
				days={selectedPlan.days}
				onBack={handleBackToPricing}
			/>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col items-center py-6 sm:p-6">
			<div className="w-full flex flex-col items-center mb-8 mt-8">
				<div className="text-purple-600 font-bold text-xl mb-8">
					<img src={ICONS.logoexpanded} alt="parhali" className="w-24" />
				</div>

				<div className="text-center px-8">
					<h1 className="font-bold mb-2 text-2xl">Ready to Get Started?</h1>
					<p className="text-sm text-gray-600">
						14 days unlimited free trial. No contract or credit card required.
					</p>
				</div>
			</div>

			<div className="w-full pb-4 flex flex-col gap-4 items-center">
				<div className="w-[85%] flex-shrink-0 flex justify-start">
					<PricingCard
						title="Daily Plan"
						price={99}
						period="day"
						features={PLAN_FEATURES.daily}
						onGetPlan={() => handleSelectPlan("daily", 99)}
					/>
				</div>

				<div className="w-[85%] flex-shrink-0 flex justify-start">
					<PricingCard
						title="Subscription Plan"
						price={1000}
						period="month"
						features={PLAN_FEATURES.subscription}
						isPrimary={true}
						onGetPlan={() => handleSelectPlan("subscription", 200)}
					/>
				</div>

				<div className="w-[85%] flex-shrink-0 flex justify-start">
					<PricingCard
						title="Custom Plan"
						features={PLAN_FEATURES.custom}
						isCustom={true}
						onGetPlan={(days?: number, price?: number) =>
							handleSelectPlan("custom", price || 0, days)
						}
					/>
				</div>
			</div>

			{/* Free Trial Button */}
			<div className="w-full mt-24 mb-10 text-center">
				<Link
					className="text-base text-accent hover:text-purple-800 transition-colors duration-300 inline-flex items-center font-medium"
					to="/dashboard"
				>
					Start my 14-day free trial
					<ArrowRight className="h-3 w-3 ml-1" />
				</Link>
			</div>
		</div>
	);
};

export default MobilePricingPage;
