import { createFile<PERSON>out<PERSON>, useNavigate } from "@tanstack/react-router";
import { ChevronDown, Search, Book } from "react-feather";
import { But<PERSON> } from "@/components/ui/button";
import { useState, useRef, useEffect } from "react";
import { useMediaQuery } from 'react-responsive';
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import type { MockTestCard as MockTestCardType } from "@/features/tests/mock.constants";
import { generateMockTest } from "@/features/tests/services";
import { useToast } from "@/hooks/use-toast";
import { useGetMockTests } from "@/lib/queries/tests.query";

// University to icon mapping
const universityIconMap: Record<string, string> = {
	NUST: "/assets/icons/nust.png",
	UET: "/assets/icons/uet.png",
	// Add more universities and their icons as needed
};

// Helper function to convert API mock test data to MockTestCard interface
const convertApiMockTestToCard = (apiTest: {
	testId: string;
	university: string;
	name: string;
	description: string;
}): MockTestCardType => {
	return {
		id: apiTest.testId,
		title: apiTest.name,
		description: apiTest.description,
		icon: universityIconMap[apiTest.university] || "/assets/icons/nust.png", // fallback to NUST icon
		entryTestCode: apiTest.testId,
	};
};

function MockTestCard({ test }: { test: MockTestCardType }) {
	const [infoExpanded, setInfoExpanded] = useState(false);
	const contentRef = useRef<HTMLDivElement>(null);
	const [contentHeight, setContentHeight] = useState<number>(0);
	const [loading, setLoading] = useState(false);
	const navigate = useNavigate();
	const { toast } = useToast();

	useEffect(() => {
		if (contentRef.current) {
			setContentHeight(infoExpanded ? contentRef.current.scrollHeight : 0);
		}
	}, [infoExpanded]);

	const handleStartTest = async () => {
		try {
			setLoading(true);
			const response = await generateMockTest({
				entryTest: test.entryTestCode, // Use the entryTestCode directly
			});

			if (response.data && response.data.success) {
				// Store test data in localStorage to avoid URL length limitations
				localStorage.setItem(
					"currentTestData",
					JSON.stringify(response.data.data)
				);

				// Navigate to MCQs page - using the correct path format
				navigate({ to: "/mcqs" });
			} else {
				throw new Error("Failed to generate test: Invalid response format");
			}
		} catch (error) {
			console.error("Failed to generate mock test:", error);
			toast({
				title: "Error",
				description: "Failed to generate mock test. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="box-sizing-border-box px-[18px] sm:px-7 py-[18px] sm:py-[22px] w-full bg-white border border-gray-300 rounded-[15px]">
				<div 
				className="flex flex-row justify-between items-start sm:items-center w-full gap-4 sm:gap-0 cursor-pointer"
				onClick={() => setInfoExpanded(!infoExpanded)}
			>
				<div className="flex flex-row items-center gap-3 sm:gap-[21px]">
					<img
						src={test.icon}
						alt={test.title}
						className="w-[30px] h-[30px] sm:w-[50px] sm:h-[50px]"
					/>
					<span className="font-inter font-medium text-base sm:text-lg leading-[24px] sm:leading-[27px] text-[#2E2E2E]">
						{test.title}
					</span>
				</div>
					<div className="flex flex-row justify-center items-center gap-2 sm:gap-5 mt-2 sm:mt-0 text-gray-400 hover:text-gray-600 transition-colors duration-300">
					<span className="hidden sm:block font-inter font-semibold text-sm sm:text-base leading-[24px] sm:leading-[27px]">
						more info
					</span>
					<ChevronDown
						className={`w-5 h-5 sm:w-6 sm:h-6  transition-transform duration-300 ease-in-out ${infoExpanded ? "transform rotate-180" : ""}`}
					/>
				</div>
			</div>

			<div
				ref={contentRef}
				className="overflow-hidden transition-all duration-500 ease-in-out w-full"
				style={{
					height: `${contentHeight}px`,
					opacity: infoExpanded ? 1 : 0,
				}}
			>
				<div className="flex flex-col items-start gap-4 sm:gap-[30px] w-full mt-[42px] sm:mt-10">
					<span className="font-inter font-bold text-base leading-[140%] tracking-[-0.01em] text-gray-400 uppercase">
						ABOUT THE TEST
					</span>

					<div className="flex flex-row justify-between items-end w-full">
						<p className="font-inter font-normal text-base leading-[140%] tracking-[-0.01em] text-gray-600">
							{test.description}
						</p>
					</div>

					<div className="flex flex-row justify-center sm:justify-end items-center w-full gap-[30px] sm:gap-[88px] mt-[26px] sm:mt-2.5">
						<Button
							className="flex flex-row justify-center items-center px-4 sm:px-6 py-2.5 gap-2.5 w-full sm:w-[232px] h-[50px] sm:h-[55px] bg-gradient-to-b from-[rgba(255,255,255,0.12)] to-[rgba(255,255,255,0)] bg-accent hover:bg-[#4c2dae] transition-colors duration-300 text-white rounded-[10px] shadow-md hover:shadow-lg"
							onClick={(e) => {
								e.stopPropagation();
								handleStartTest();
							}}
							disabled={loading}
						>
							{loading ? (
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
							) : (
								<Book className="w-4 h-4 text-white" />
							)}
							<span className="font-inter font-medium text-sm leading-[140%] tracking-[-0.01em] text-center text-white">
								{loading ? "Loading..." : "Start Test"}
							</span>
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}

const MockTestPage = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });
	const [searchQuery, setSearchQuery] = useState("");

	// Fetch mock tests from API
	const { data: apiMockTests, isLoading, error } = useGetMockTests();

	// Convert API data to MockTestCard format
	const mockTestsData = apiMockTests?.mockTests
		? apiMockTests.mockTests.map(convertApiMockTestToCard)
		: [];

	// Filter mock tests based on search query
	const filteredTests = mockTestsData.filter(
		(test) =>
			test.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			test.description.toLowerCase().includes(searchQuery.toLowerCase())
	);

	// Show loading state
	if (isLoading) {
		return (
			<>
				{!isDesktop && (
					<>
						<Navbar />
						<MobileSwitcher />
					</>
				)}
				<div className="flex flex-col items-center p-4 sm:p-6 gap-6 sm:gap-10 w-full max-w-[1160px] mx-auto pb-20 lg:pb-4">
					<div className="flex justify-center items-center h-64">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
					</div>
				</div>
			</>
		);
	}

	// Show error state
	if (error) {
		return (
			<>
				{!isDesktop && (
					<>
						<Navbar />
						<MobileSwitcher />
					</>
				)}
				<div className="flex flex-col items-center p-4 sm:p-6 gap-6 sm:gap-10 w-full max-w-[1160px] mx-auto pb-20 lg:pb-4">
					<div className="flex flex-col items-center justify-center h-64 gap-4">
						<p className="font-inter text-[#475569] text-lg text-center">
							Failed to load mock tests. Please try again later.
						</p>
						<Button
							onClick={() => window.location.reload()}
							className="bg-accent hover:bg-[#4c2dae]"
						>
							Retry
						</Button>
					</div>
				</div>
			</>
		);
	}

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<div className="flex flex-col items-center p-4 sm:p-6 gap-6 sm:gap-10 w-full max-w-[1160px] mx-auto pb-20 lg:pb-4">
				<div className="flex flex-col lg:flex-row justify-between items-start lg:items-center px-2 sm:px-[25px] w-full gap-6 lg:gap-0">
					<div className="flex flex-col items-start gap-2 sm:gap-[15px] w-full">
						<h1 className="font-inter font-bold text-2xl sm:text-[32px] leading-[30px] sm:leading-[39px] text-[#211C37]">
							Mock Test
						</h1>
						<p className="font-inter font-normal text-sm sm:text-base leading-[18px] sm:leading-[19px] text-[#64748B] pr-4">
							The ultimate real-exam practice experience. <br />
							{/* Perfect replicas of actual university entrance exams with <br /> */}
							Identical MCQ counts, subject weightages, and time limits.{" "}
						</p>
					</div>

					<div className="flex flex-col items-start w-full lg:w-auto max-w-full lg:max-w-[413px]">
						<div className="box-border flex flex-row items-center px-[14px] py-4 sm:py-[27px] gap-[10px] w-full lg:w-[413px] h-[45px] sm:h-[55px] bg-white border border-solid border-[#EDF1F3] shadow-sm rounded-[10px] focus-within:border-accent focus-within:shadow-md transition-all duration-300">
							<div className="flex flex-row items-center gap-3 w-full">
								<Search className="w-5 h-5 text-gray-400" />
								<input
									type="text"
									placeholder="Search mock tests..."
									className="w-full border-none outline-none font-inter font-normal text-sm sm:text-base leading-[140%] tracking-[-0.01em] text-[#6C7278]"
									value={searchQuery}
									onChange={(e) => setSearchQuery(e.target.value)}
								/>
							</div>
						</div>
					</div>
				</div>

				<div className="flex flex-col items-start gap-5 sm:gap-[30px] w-full">
					{filteredTests.length > 0 ? (
						filteredTests.map((test) => (
							<MockTestCard key={test.id} test={test} />
						))
					) : (
						<div className="w-full text-center py-8">
							<p className="font-inter text-[#475569] text-lg">
								No mock tests found matching your search.
							</p>
						</div>
					)}
				</div>
			</div>
		</>
	);
};

// The path needs to match a valid path in the route tree
export const Route = createFileRoute("/(app)/_mainlayout/t/mock")({
	component: MockTestPage,
});

export default MockTestPage;
