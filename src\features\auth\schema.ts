import { z } from "zod";
import { isValidPhoneNumber } from "react-phone-number-input";

export const loginValidation = z.object({
	email: z
		.string()
		.min(2, "Email is required")
		.max(50, "Email is too long")
		.email("Please enter a valid email address"),
	password: z
		.string()
		.min(2, "Password is required")
		.max(50, "Password is too long"),
});

export const registerValidation = z
	.object({
		fullName: z
			.string()
			.min(2, "Full name is required")
			.max(50, "Full name is too long"),
		email: z
			.string()
			.min(2, "Email is required")
			.max(50, "Email is too long")
			.email("Please enter a valid email address"),
		password: z
			.string()
			.min(8, "Password must be at least 8 characters")
			.max(50, "Password is too long")
			.regex(/[A-Z]/, "Password must contain at least one uppercase letter")
			.regex(/[a-z]/, "Password must contain at least one lowercase letter")
			.regex(/[0-9]/, "Password must contain at least one number"),
		confirmPassword: z.string().min(2, "Please confirm your password"),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords do not match",
		path: ["confirmPassword"],
	});

export const personalInfoValidation = z.object({
	phoneNumber: z
		.string()
		.refine((val) => !val || isValidPhoneNumber(val), {
			message: "Please enter a valid phone number",
		})
		.optional(),
	city: z.string().min(1, "City is required").max(50, "City name is too long"),
	institute: z
		.string()
		.min(1, "Institute name is required")
		.max(100, "Institute name is too long"),
	educationBackground: z.string().min(1, "Education background is required"),
	currentClass: z.string().min(1, "Current class is required"),
});

export const educationalBackgroundValidation = z.object({
	subjectGroup: z
		.array(z.string())
		.min(1, "Please select at least one subject group"),
	targetEntryTests: z
		.array(z.string())
		.min(1, "Please select at least one entry test"),
});

export const emptyValidation = z.object({ takeTrialTest: z.boolean() });

export const onboardUserValidation = z.object({
	...personalInfoValidation.shape,
	...educationalBackgroundValidation.shape,
	...emptyValidation.shape,
});

export const updateUserValidation = z.object({
	name: z
		.string()
		.min(2, "Name is required")
		.max(50, "Name is too long")
		.optional(),
	email: z.string().email("Please enter a valid email").optional(),
	phoneNumber: z
		.string()
		.min(13, "Phone number is too short")
		.max(13, "Phone number is too long")
		.refine((val) => !val || isValidPhoneNumber(val), {
			message: "Please enter a valid phone number",
		})
		.optional(),
	city: z.string().optional(),
	institute: z.string().optional(),
	educationBackground: z.string().optional(),
	currentClass: z.string().optional(),
	subjectGroup: z.array(z.string()).optional(),
	targetEntryTests: z.array(z.string()).optional(),
});

export const resetPasswordValidation = z.object({
	email: z
		.string()
		.min(2, "Email is required")
		.max(50, "Email is too long")
		.email("Please enter a valid email address"),
});

export type PersonalInfoValidation = z.infer<typeof personalInfoValidation>;
export type LoginValidation = z.infer<typeof loginValidation>;
export type RegisterValidation = z.infer<typeof registerValidation>;
export type EducationalBackgroundValidation = z.infer<
	typeof educationalBackgroundValidation
>;
export type EmptyValidation = z.infer<typeof emptyValidation>;
export type OnboardUserValidation = z.infer<typeof onboardUserValidation>;
export type UpdateUserValidation = z.infer<typeof updateUserValidation>;
export type ResetPasswordValidation = z.infer<typeof resetPasswordValidation>;
