import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import type { FunctionComponent } from "@/common/types";
import { routeTree } from "./routeTree.gen";
import { useAuthStore } from "@/features/auth/store";
import Splash from "@/components/ui/splash";
import { useShallow } from "zustand/react/shallow";
import { useEffect } from "react";
import { onAuthStateChanged } from "firebase/auth";
import auth from "@/lib/config/firebase";
import { Toaster } from "@/components/ui/toaster";
// import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

const queryClient = new QueryClient();
const router = createRouter({
	routeTree,
	context: {
		auth: undefined!,
	},
});
type AppProps = { router: typeof router };

const App = ({ router }: AppProps): FunctionComponent => {
	const {
		isLoggedIn,
		loading,
		user,
		error,
		checkingAuth,
		isNewUser,
		setCheckingAuth,
		setUser,
		setLoading,
		setLoggedIn,
		setIsNewUser,
		setFirebaseToken,
	} = useAuthStore(
		useShallow((state) => ({
			isLoggedIn: state.isLoggedIn,
			loading: state.loading,
			user: state.user,
			error: state.error,
			checkingAuth: state.checkingAuth,
			isNewUser: state.isNewUser,
			setCheckingAuth: state.setCheckingAuth,
			setUser: state.setUser,
			setLoading: state.setLoading,
			setLoggedIn: state.setLoggedIn,
			setIsNewUser: state.setIsNewUser,
			setFirebaseToken: state.setFirebaseToken,
		}))
	);

	// TEMPORARILY MOCK AUTH FOR DEVELOPMENT - REMOVE IN PRODUCTION
	// const authContext = {
	// 	isLoggedIn: true, // Force logged in state
	// 	loading: false,
	// 	user:
	// 		user ||
	// 		({
	// 			displayName: "Demo User",
	// 			email: "<EMAIL>",
	// 			uid: "demo-uid",
	// 		} as any), // Provide mock user if none exists
	// 	error,
	// 	isNewUser: false,
	// };

	const authContext = {
		isLoggedIn,
		loading,
		user,
		error,
		isNewUser,
	};

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
			if (firebaseUser) {
				const firebaseToken = await firebaseUser.getIdToken(true);
				setFirebaseToken(firebaseToken);
				setUser(firebaseUser);
				const onboarded = (await firebaseUser.getIdTokenResult()).claims[
					"onboarded"
				];
				if (!onboarded) {
					setIsNewUser(true);
				} else {
					setLoggedIn(!!firebaseUser);
				}
			} else {
				setUser(null);
				setLoggedIn(false);
			}
			setLoading(false);
			setCheckingAuth(false);
			router.invalidate();
		});
		return unsubscribe;
	}, []);

	return (
		<QueryClientProvider client={queryClient}>
			{checkingAuth ? (
				<Splash />
			) : (
				<RouterProvider router={router} context={{ auth: authContext }} />
			)}
			{/* <TanStackRouterDevelopmentTools
				router={router}
				initialIsOpen={false}
				position="bottom-right"
			/>*/}
			{/* <ReactQueryDevtools initialIsOpen={false} /> */}
			<Toaster />
		</QueryClientProvider>
	);
};

export default App;
