import { But<PERSON> } from "@/components/ui/button";
import { routeList } from "@/lib/route-list";
import { BookOpen, Clipboard } from "react-feather";
import { Link } from "@tanstack/react-router";

const MobileSwitcher = () => {
	const pathname = window.location.pathname;
	const currentMenu = routeList
		.find((menu) => menu.menus.some((m) => m.href === pathname))
		?.menus.find((m) => m.href === pathname);
	return (
		<div className="fixed bottom-5 lg:hidden w-full flex justify-center z-50">
			<div className="h-20 gap-x-2 p-2.5 grid-cols-2 rounded-2xl shadow-xl gap-2.5 bg-white grid max-w-sm">
				<Button
					variant={currentMenu?.href === "/dashboard" ? "default" : "outline"}
					className="rounded-xl flex h-full p-2"
					asChild
				>
					<Link to="/dashboard">
						<BookOpen size={24} />
						Dashboard
					</Link>
				</Button>
				<Button
					variant={currentMenu?.href === "/attempt" ? "default" : "outline"}
					className="rounded-xl flex h-full p-2"
					asChild
				>
					<Link to="/attempt">
						<Clipboard size={24} />
						Attempt
					</Link>
				</Button>
			</div>
		</div>
	);
};

export default MobileSwitcher;
