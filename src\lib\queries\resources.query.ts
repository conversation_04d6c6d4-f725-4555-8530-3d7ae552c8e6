import { getResources, getFilters } from "@/features/resources/services";
import { FilterParams } from "@/features/resources/types";
import { useQuery, useQueries } from "@tanstack/react-query";

export const useGetFilters = (
	subject: string,
	resourceType: string,
	chapters?: string[]
) =>
	useQuery({
		queryKey: ["filters", { subject, resourceType, chapters }],
		queryFn: () => getFilters(subject, resourceType, chapters), // Don't pass filters to get all available options
		enabled: !!subject && !!resourceType,
		select: (response) => response.data.data,
		retry: 0,
	});

// Hook for desktop: single resource type with pagination
export const useGetResourcesWithPagination = (
	subject: string,
	resourceType: string,
	searchTerm: string,
	filters: FilterParams,
	currentPage: number,
	pageSize: number,
	isDesktop: boolean
) =>
	useQuery({
		queryKey: [
			"resources",
			{
				type: resourceType,
				selectedSubject: subject,
				searchTerm,
				filters,
				currentPage,
				pageSize,
			},
		],
		queryFn: () =>
			getResources(
				subject,
				resourceType,
				searchTerm,
				filters,
				currentPage,
				pageSize
			),
		enabled: !!subject && !!resourceType && isDesktop,
		retry: 0,
	});

// Hook for mobile: all resource types
export const useGetAllResourceTypes = (
	subject: string,
	searchTerm: string,
	filters: FilterParams,
	currentPage: number,
	pageSize: number,
	isDesktop: boolean
) =>
	useQueries({
		queries: ["video", "file", "link"].map((type) => ({
			queryKey: [
				"resources",
				{
					type,
					selectedSubject: subject,
					searchTerm,
					filters,
					currentPage,
					pageSize,
				},
			],
			queryFn: () =>
				getResources(subject, type, searchTerm, filters, currentPage, pageSize),
			enabled: !!subject && !isDesktop,
			retry: 0,
		})),
	});
