// Utility functions for test analytics

export interface MCQResponse {
	mcqId: string;
	chosenOption: number;
}

export interface MCQDetail {
	_id: string;
	subject: string;
	type: string;
	difficulty: "easy" | "medium" | "hard";
	answer: number;
	topic: string;
}

export interface CalculatedAnalytics {
	totalMcqs: number;
	correctAnswers: number;
	wrongAnswers: number;
	subjects: Record<string, { correct: number; wrong: number }>;
	mcqsType: Record<string, { correct: number; wrong: number }>;
	difficultyType: Record<string, { correct: number; wrong: number }>;
	subject_division: Record<string, {
		difficulty: Record<string, { correct: number; wrong: number }>;
		type: Record<string, { correct: number; wrong: number }>;
		topics: Record<string, { correct: number; wrong: number }>;
	}>;
}

export const calculateQuizAnalytics = (
	mcqResponses: MCQResponse[],
	mcqDetails: MCQDetail[]
): CalculatedAnalytics => {
	const analytics: CalculatedAnalytics = {
		totalMcqs: mcqResponses.length,
		correctAnswers: 0,
		wrongAnswers: 0,
		subjects: {},
		mcqsType: {},
		difficultyType: {},
		subject_division: {},
	};

	// Create a map for quick MCQ detail lookup
	const mcqDetailMap = new Map<string, MCQDetail>();
	mcqDetails.forEach(mcq => {
		mcqDetailMap.set(mcq._id, mcq);
	});

	// Process each response
	mcqResponses.forEach(response => {
		const mcqDetail = mcqDetailMap.get(response.mcqId);
		if (!mcqDetail) return;

		const isCorrect = response.chosenOption === mcqDetail.answer;
		const subject = mcqDetail.subject;
		const type = mcqDetail.type;
		const difficulty = mcqDetail.difficulty;
		const topic = mcqDetail.topic;

		// Update overall counts
		if (isCorrect) {
			analytics.correctAnswers++;
		} else {
			analytics.wrongAnswers++;
		}

		// Initialize subject if not exists
		if (!analytics.subjects[subject]) {
			analytics.subjects[subject] = { correct: 0, wrong: 0 };
		}
		if (!analytics.subject_division[subject]) {
			analytics.subject_division[subject] = {
				difficulty: {},
				type: {},
				topics: {},
			};
		}

		// Update subject counts
		if (isCorrect) {
			analytics.subjects[subject].correct++;
		} else {
			analytics.subjects[subject].wrong++;
		}

		// Initialize and update MCQ type counts
		if (!analytics.mcqsType[type]) {
			analytics.mcqsType[type] = { correct: 0, wrong: 0 };
		}
		if (isCorrect) {
			analytics.mcqsType[type].correct++;
		} else {
			analytics.mcqsType[type].wrong++;
		}

		// Initialize and update difficulty type counts
		if (!analytics.difficultyType[difficulty]) {
			analytics.difficultyType[difficulty] = { correct: 0, wrong: 0 };
		}
		if (isCorrect) {
			analytics.difficultyType[difficulty].correct++;
		} else {
			analytics.difficultyType[difficulty].wrong++;
		}

		// Update subject division - difficulty
		if (!analytics.subject_division[subject].difficulty[difficulty]) {
			analytics.subject_division[subject].difficulty[difficulty] = { correct: 0, wrong: 0 };
		}
		if (isCorrect) {
			analytics.subject_division[subject].difficulty[difficulty].correct++;
		} else {
			analytics.subject_division[subject].difficulty[difficulty].wrong++;
		}

		// Update subject division - type
		if (!analytics.subject_division[subject].type[type]) {
			analytics.subject_division[subject].type[type] = { correct: 0, wrong: 0 };
		}
		if (isCorrect) {
			analytics.subject_division[subject].type[type].correct++;
		} else {
			analytics.subject_division[subject].type[type].wrong++;
		}

		// Update subject division - topics
		if (!analytics.subject_division[subject].topics[topic]) {
			analytics.subject_division[subject].topics[topic] = { correct: 0, wrong: 0 };
		}
		if (isCorrect) {
			analytics.subject_division[subject].topics[topic].correct++;
		} else {
			analytics.subject_division[subject].topics[topic].wrong++;
		}
	});

	return analytics;
};
