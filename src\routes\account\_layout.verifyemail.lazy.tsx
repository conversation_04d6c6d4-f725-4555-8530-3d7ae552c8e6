import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { createLazyFileRoute, useNavigate } from '@tanstack/react-router' // Import useNavigate
import { useShallow } from 'zustand/react/shallow'
import { useAuthStore } from '@/features/auth/store'

const Login = () => {
  const navigate = useNavigate() // Initialize navigate
  const { sendFirebaseEmailVerification, user } = useAuthStore(
    useShallow((state) => ({
      sendFirebaseEmailVerification: state.sendFirebaseEmailVerification,
      user: state.user,
    }))
  )

  const [timer, setTimer] = useState(60) // start disabled for 60 seconds

  useEffect(() => {
    if (timer === 0) return
    const intervalId = setInterval(() => {
      setTimer((prev) => prev - 1)
    }, 1000)
    return () => clearInterval(intervalId)
  }, [timer])

  const onSubmit = () => {
    sendFirebaseEmailVerification(user)
    setTimer(60) // reset timer on resend
  }

  const handleGoToLogin = () => {
    navigate({to: "/account/login"}) // Navigate to your login route
  }

  return (
    <div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
      <h1 className="text-lg font-semibold mb-4">Email Verification</h1>
      <p className="text-gray-600 text-center max-w-[450px] px-4">
        Please check your email for a verification link to complete your signup.
      </p>
      <div className="w-full flex flex-col gap-y-2">
        <Button
          onClick={onSubmit}
          type="submit"
          className="w-full text-xs sm:text-sm"
          disabled={timer > 0}
        >
          {timer > 0 ? `Send Link Again (${timer}s)` : 'Send Link Again'}
        </Button>
        {/* Go to Login Button */}
        <Button
          onClick={handleGoToLogin}
          variant="outline" // Use an outline variant for secondary action
          className="w-full text-xs sm:text-sm mt-2" // Add some top margin
        >
          Go to Login
        </Button>
        <p className="text-gray-500 text-center text-sm mt-1">
          Didn't receive the email? Check your spam folder or{' '}
          <span>send again.</span>
        </p>
      </div>
    </div>
  )
}

export const Route = createLazyFileRoute('/account/_layout/verifyemail')({
  component: Login,
})