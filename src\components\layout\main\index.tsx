import { Sidebar } from "./sidebar";
import { useSidebar } from "@/hooks/use-sidebar";
import { Navbar } from "./navbar";
import { cn } from "@/lib/utils";
import { useStore } from "@/hooks/use-store";
import { useMediaQuery } from 'react-responsive';

export default function Layout({ children }: { children: React.ReactNode }) {
	const isDesktop = useMediaQuery({ minWidth: 1024 });
	const sidebar = useStore(useSidebar, (x) => x);
	if (!sidebar) return null;
	const { getOpenState, settings } = sidebar;

	return (
		<>
			{isDesktop && <Navbar />}
			<Sidebar />
			<main
				className={cn(
					"min-h-[calc(100vh_-_90px)] bg-background transition-[margin-left] ease-in-out duration-300",
					!settings.disabled && (!getOpenState() ? "lg:ml-[90px]" : "lg:ml-72")
				)}
			>
				{children}
			</main>
		</>
	);
}
