import Splash from "@/components/ui/splash";
import { ICONS } from "@/lib/assets/images";
import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/account/_layout")({
	validateSearch: (search) => {
		if (search["redirect"]) {
			return {
				redirect: search["redirect"] as string,
			};
		}
		return {};
	},
	beforeLoad: ({ context, location, search }) => {
		if (context.auth.isLoggedIn) {
			throw redirect({
				// to: search.redirect ?? "/selectplan",
				to: search.redirect ?? "/dashboard",
			});
		} else if (location.pathname === "/account/personalinfo") {
			if (!context.auth.isNewUser) {
				throw redirect({
					to: "/account/login",
				});
			}
		}
	},
	pendingComponent: Splash,
	component: () => (
		<div className="min-h-screen w-full bg-gradient-to-r from-[#CBBBFF] to-white relative overflow-hidden pb-8">
			<div className="w-full p-8 flex justify-center lg:justify-start">
				<img src={ICONS.logoexpanded} className="h-7 sm:h-10 lg:h-12" />
			</div>
			{/* <Spot className="absolute z-0 -top-4 -right-4 size-[200px] md:size-[250px] lg:size-[350px] xl:size-[500px] aspect-square opacity-70" />
			<Spot className="absolute z-0 -bottom-4 -left-4 size-[200px] md:size-[250px] lg:size-[350px] xl:size-[500px] aspect-square opacity-70" /> */}
			<div className="relative w-full flex flex-col items-center justify-center z-10 px-8 grow">
				<Outlet />
			</div>
		</div>
	),
});
