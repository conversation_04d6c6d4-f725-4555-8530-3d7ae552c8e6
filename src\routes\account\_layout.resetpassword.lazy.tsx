import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createLazyFileRoute, useNavigate } from "@tanstack/react-router";
import {
	resetPasswordValidation,
	ResetPasswordValidation,
} from "@/features/auth/schema";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { formatErrorMsg } from "@/helpers/firebase";
import { useErrorToast } from "@/hooks/use-error-toast";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";

const ResetPassword = () => {
	const showError = useErrorToast();
	const navigate = useNavigate();
	const [emailSent, setEmailSent] = useState(false);

	const { resetPassword, loading } = useAuthStore(
		useShallow((state) => ({
			resetPassword: state.resetPassword,
			loading: state.loading,
		}))
	);

	const form = useForm<ResetPasswordValidation>({
		resolver: zodResolver(resetPasswordValidation),
		defaultValues: { email: "" },
	});

	const onSubmit = async (values: ResetPasswordValidation) => {
		const result = await resetPassword(values.email);
		if (result instanceof Error) {
			showError(formatErrorMsg(result.message));
		} else {
			setEmailSent(true);
			toast({
				title: "Reset email sent!",
				description: "Please check your email for password reset instructions.",
			});
		}
	};

	if (emailSent) {
		return (
			<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
				<h1>Check Your Email</h1>
				<p className="text-gray-600 text-center max-w-[400px]">
					We've sent a password reset link to your email address. Please check
					your inbox and follow the instructions to reset your password.
				</p>
				<div className="w-full flex flex-col gap-y-2">
					<Button
						onClick={() => navigate({ to: "/account/login" })}
						className="w-full text-xs sm:text-sm"
					>
						Back to Login
					</Button>
					<Button
						variant="outline"
						onClick={() => setEmailSent(false)}
						className="w-full text-xs sm:text-sm"
					>
						Try Another Email
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<h1>Reset Password</h1>
			<p className="text-gray-600 text-center max-w-[400px]">
				Type your authorized email address to receive reset password link.
			</p>
			<div className="w-full flex flex-col gap-y-2">
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="gap-y-4 flex flex-col"
					>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Email
									</FormLabel>
									<FormControl>
										<Input
											placeholder="<EMAIL>"
											type="email"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button
							type="submit"
							className="w-full text-xs sm:text-sm"
							disabled={loading}
							loading={loading}
						>
							Send Reset Link
						</Button>
					</form>
				</Form>
				<Button
					variant="outline"
					onClick={() => navigate({ to: "/account/login" })}
					className="w-full text-xs sm:text-sm"
				>
					Back to Login
				</Button>
			</div>
		</div>
	);
};

export const Route = createLazyFileRoute("/account/_layout/resetpassword")({
	component: ResetPassword,
});
