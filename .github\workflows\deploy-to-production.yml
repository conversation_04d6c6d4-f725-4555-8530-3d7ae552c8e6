name: Deploy to Production

on:
  workflow_dispatch:

# Version Configuration
env:
  MAJOR_VERSION: "0"

jobs:
  BuildApplication:
    name: Build Frontend Core
    runs-on: ubuntu-latest

    env:
      PRODUCTION_GTM_ID: GTM-PN5GHZLP

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc

      - name: Setup yarn v4
        run: corepack enable && corepack prepare yarn@4.5.1

      - name: Install dependencies
        run: yarn install

      - name: Create .env file
        run: |
          echo "VITE_API_URL=${{ secrets.PRODUCTION_API_URL }}" >> .env
          echo "VITE_FIREBASE_API_KEY=${{ secrets.PRODUCTION_FIREBASE_API_KEY }}" >> .env
          echo "VITE_FIREBASE_AUTH_DOMAIN=${{ secrets.PRODUCTION_FIREBASE_AUTH_DOMAIN }}" >> .env
          echo "VITE_GTM_ID=${{ env.PRODUCTION_GTM_ID }}" >> .env
          echo "VITE_APP_ENVIRONMENT=production" >> .env
          echo "VITE_PUBLIC_POSTHOG_KEY=phc_StA3Hw0V1r31SCcW98YPFU3XgUQICUNqHX6NHm0JsDW" >> .env
          echo "VITE_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com" >> .env

      - name: Build
        run: yarn run build

      - name: Archive production artifacts
        uses: actions/upload-artifact@v4
        with:
          path: dist
          name: dist
          retention-days: 1
          overwrite: true

  DeployToProduction:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [BuildApplication]

    steps:
      - name: Download production artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.PRODUCTION_AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.PRODUCTION_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Push files to s3
        run: aws s3 sync dist s3://${{ secrets.PRODUCTION_FRONTEND_CORE_BUCKET }}/ --delete

  CreateProductionRelease:
    name: Create Production Release
    runs-on: ubuntu-latest
    needs: DeployToProduction
    steps:
      - name: Checkout code for release creation
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate release version
        id: generate_version
        run: |
          # Determine environment prefix
          PREFIX="prod-v${{ env.MAJOR_VERSION }}."

          # Get latest release with this prefix using GitHub API
          # Sort numerically by extracting the minor version
          LATEST_RELEASE=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/releases" | \
            jq -r --arg prefix "$PREFIX" '
              [
                .[] |
                select(.tag_name | startswith($prefix)) |
                {
                  tag: .tag_name,
                  # Extract the minor version number after the prefix and convert to number
                  minor_num: (.tag_name | sub($prefix; "") | tonumber? // 0)
                }
              ] |
              sort_by(.minor_num) |
              last.tag // empty
            ')

          if [[ -z "$LATEST_RELEASE" || "$LATEST_RELEASE" == "null" ]]; then
            # No previous releases found, start with .1
            MINOR_VERSION="1"
          else
            # Extract minor version and increment
            # Use grep with PCRE to extract only the digits after the prefix
            CURRENT_MINOR=$(echo "$LATEST_RELEASE" | grep -oP "(?<=${PREFIX})\d+" || echo "0")
            if [[ -z "$CURRENT_MINOR" ]]; then
                # Fallback if grep doesn't find anything (e.g., malformed tag or prefix mismatch)
                CURRENT_MINOR="0"
            fi
            MINOR_VERSION=$((CURRENT_MINOR + 1))
          fi

          # Create new version
          NEW_VERSION="${PREFIX}${MINOR_VERSION}"

          echo "version=${NEW_VERSION}" >> $GITHUB_OUTPUT
          echo "date=$(date +'%Y-%m-%d %H:%M:%S')" >> $GITHUB_OUTPUT

          echo "🏷️ Generated version: ${NEW_VERSION}"

      - name: Get last commit title from main
        id: last_commit
        run: |
          # Ensure the workspace is a safe directory for Git operations
          git config --global --add safe.directory "$GITHUB_WORKSPACE"

          # Fetch only the latest commit from the 'main' branch without checking it out
          git fetch origin main:main-remote --depth=1

          # Get the last commit title from the fetched 'main-remote' reference
          LAST_COMMIT_TITLE=$(git log -1 --pretty=%s main-remote)
          echo "title=${LAST_COMMIT_TITLE}" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          name: "${{ steps.generate_version.outputs.version }}"
          tag_name: ${{ steps.generate_version.outputs.version }}
          body: |
            ### ${{ steps.last_commit.outputs.title }}
            **Deployed at:** ${{ steps.generate_version.outputs.date }}
            **Branch:** ${{ github.ref_name }}
            **Commit:** ${{ github.sha }}

            This is an automated release deployed to the production environment.
          draft: false
          prerelease: false
          make_latest: true
