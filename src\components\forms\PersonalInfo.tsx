import { But<PERSON> } from "@/components/ui/button";
import { useFormContext } from "react-hook-form";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { PersonalInfoValidation } from "@/features/auth/schema";
import { PhoneInput } from "@/components/ui/phone-input";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronDown, Search } from "react-feather";
import { cities, institutes } from "@/lib/constants/onboardingvalues";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ICONS } from "@/lib/assets/images";
import { useState } from "react";

const PersonalInfo = () => {
	const form = useFormContext<PersonalInfoValidation>();
	const [cityOpen, setCityOpen] = useState(false);
	const [instituteOpen, setInstituteOpen] = useState(false);

	return (
		<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<h1 className="flex gap-x-2">
				Welcome to{" "}
				<img src={ICONS.logoexpanded} className="h-7 sm:h-10 lg:h-11" />
			</h1>
			<p className="text-gray-600 text-center max-w-[400px]">
				Get ready to start your personalized journey towards acing your entry
				test. Let’s set you up for success!
			</p>
			<div className="w-full flex flex-col gap-y-2">
				<FormField
					control={form.control}
					name="phoneNumber"
					render={({ field }) => (
						<FormItem className="space-y-1">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								Phone Number <span className="text-gray-400">(Optional)</span>
							</FormLabel>
							<FormControl>
								<PhoneInput
									international={true}
									defaultCountry="PK"
									placeholder="Enter a phone number"
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="city"
					render={({ field }) => (
						<FormItem className="flex flex-col space-y-1">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								City
							</FormLabel>
							<Popover open={cityOpen} onOpenChange={setCityOpen}>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant="outline"
											role="combobox"
											className={cn(
												"justify-between overflow-hidden rounded-[6px] border-slate-300 bg-white ring-offset-0 hover:bg-white focus-visible:ring-0",
												!field.value && "text-muted-foreground"
											)}
										>
											<div className="flex items-center">
												<Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />{" "}
												{field.value
													? field.value === "other"
														? "Other"
														: cities.find((city) => city.value === field.value)
																?.label
													: "Enter or Search your City"}
											</div>
											<ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									side="bottom"
									avoidCollisions={false}
									className="p-0"
								>
									<Command>
										<CommandInput placeholder="Search City..." />
										<CommandList>
											<ScrollArea className="h-72">
												<CommandEmpty>No City Found.</CommandEmpty>
												<CommandGroup>
													{cities.map((city) => (
														<CommandItem
															className="aria-selected:bg-slate-100"
															key={city.value}
															value={city.value}
															onSelect={() => {
																field.onChange(city.value);
																setCityOpen(false);
															}}
														>
															<Check
																className={cn(
																	"mr-2 h-4 w-4",
																	city.value === field.value
																		? "opacity-100"
																		: "opacity-0"
																)}
															/>
															{city.label}
														</CommandItem>
													))}
												</CommandGroup>
											</ScrollArea>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="institute"
					render={({ field }) => (
						<FormItem className="flex flex-col space-y-1">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								School/College
							</FormLabel>
							<Popover open={instituteOpen} onOpenChange={setInstituteOpen}>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant="outline"
											role="combobox"
											className={cn(
												"justify-between overflow-hidden rounded-[6px] border-slate-300 bg-white ring-offset-0 hover:bg-white focus-visible:ring-0",
												!field.value && "text-muted-foreground"
											)}
										>
											<div className="flex items-center">
												<Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />{" "}
												{field.value
													? institutes.find(
															(inst) => inst.value === field.value
														)?.label
													: "Enter or Search your Institution"}
											</div>
											<ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									side="bottom"
									avoidCollisions={false}
									className="p-0"
								>
									<Command>
										<CommandInput placeholder="Search School..." />
										<CommandList>
											<CommandEmpty>No School Found.</CommandEmpty>
											<CommandGroup>
												{institutes.map((inst) => (
													<CommandItem
														className="aria-selected:bg-slate-100"
														key={inst.value}
														onSelect={() => {
															form.setValue("institute", inst.value);
															setInstituteOpen(false);
														}}
													>
														<Check
															className={cn(
																"mr-2 h-4 w-4",
																inst.value === field.value
																	? "opacity-100"
																	: "opacity-0"
															)}
														/>
														{inst.label}
													</CommandItem>
												))}
											</CommandGroup>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="educationBackground"
					render={({ field }) => (
						<FormItem className="flex flex-col space-y-1">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								Education System
							</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="px-4 border-slate-300 bg-white">
										<SelectValue placeholder="Select your education system" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="fsc">FSc. (Pakistani)</SelectItem>
									<SelectItem value="o\alevel">
										O/A Level (Cambridge)
									</SelectItem>
									<SelectItem value="other">Other</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="currentClass"
					render={({ field }) => (
						<FormItem className="flex flex-col space-y-1">
							<FormLabel className="text-gray-500 text-xs sm:text-sm">
								Year
							</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger className="px-4 border-slate-300 bg-white">
										<SelectValue placeholder="What year are you currently in?" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="11">First Year / O-levels</SelectItem>
									<SelectItem value="12">Second Year / A-levels</SelectItem>
									<SelectItem value="others">Graduated / Others</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<Button type="submit" className="w-full mt-4">
					Next
				</Button>
			</div>
		</div>
	);
};

export default PersonalInfo;
