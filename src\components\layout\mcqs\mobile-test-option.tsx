import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Bookmark, ExternalLink } from "react-feather";
import { addBookmark, deleteBookmark } from "@/features/bookmarks/services";
import { toast } from "@/hooks/use-toast";

type MobileTestOptionsProps = {
	liveCheckEnabled: boolean;
	setLiveCheckEnabled: (enabled: boolean) => void;
	showResults?: boolean;
	test?: any;
	bookmarkList?: any;
	setRefetchBookmarks?: any;
};

const MobileTestOptions = ({
	liveCheckEnabled,
	setLiveCheckEnabled,
	showResults = false,
	test,
	bookmarkList,
	setRefetchBookmarks,
}: MobileTestOptionsProps) => {
	const isTestBookmarked =
		test &&
		bookmarkList?.quiz?.quizzes?.filter((item: any) => item?._id === test?.id)
			.length
			? true
			: false;

	const removeTestBookmark = async () => {
		if (!test) return;
		const response = await deleteBookmark({ category: "quiz", id: test.id });
		console.log("removeTestBookmark", response);
		if (response?.data?.status === 200 && response?.data?.success) {
			toast({
				title: "Success",
				description: "Test bookmark removed!",
			});
		} else {
			toast({
				title: "Error",
				description: "Failed to remove the test bookmark",
				variant: "destructive",
			});
		}
	};

	const saveTestBookmark = async () => {
		if (!test) return;
		const response = await addBookmark({ category: "quiz", id: test.id });
		console.log("saveTestBookmark", response);
		if (response?.data?.status === 200 && response?.data?.success) {
			toast({
				title: "Success",
				description: "Test bookmarked!",
			});
		} else {
			toast({
				title: "Error",
				description: "Failed to bookmark the test",
				variant: "destructive",
			});
		}
	};

	const toggleTestBookmark = async () => {
		if (isTestBookmarked) {
			await removeTestBookmark();
		} else {
			await saveTestBookmark();
		}
		if (setRefetchBookmarks) {
			setRefetchBookmarks((prev: any) => !prev);
		}
	};
	return (
		<div className="space-y-3 py-2">
			<div className="flex justify-between items-center py-2">
				<span
					className={`font-medium ${showResults ? "text-gray-400" : "text-gray-700"}`}
				>
					Live Check
				</span>
				<Switch
					checked={liveCheckEnabled}
					onCheckedChange={setLiveCheckEnabled}
					disabled={showResults}
				/>
			</div>

			<div
				className={`flex justify-between items-center py-2 rounded-md px-2 -mx-2 transition-colors ${
					test
						? "cursor-pointer hover:bg-gray-50"
						: "cursor-not-allowed opacity-50"
				}`}
				onClick={test ? toggleTestBookmark : undefined}
			>
				<span className="text-gray-700 font-medium">Save Test</span>
				<Bookmark
					className={`h-4 w-4 ${
						isTestBookmarked ? "fill-accent text-accent" : "text-gray-500"
					}`}
				/>
			</div>

			<div className="flex justify-between items-center py-2">
				<span className="text-gray-700 font-medium">See Test Info</span>
				<Button
					variant="ghost"
					size="icon"
					className="text-gray-500 h-8 w-8 p-0"
				>
					<ExternalLink className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
};

export default MobileTestOptions;
