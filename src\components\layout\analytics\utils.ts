import { AnalyticsData, CHART_COLORS } from './types';

// Utility functions
export const formatName = (name: string): string => {
	return name === name.toUpperCase() 
		? name 
		: name.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
};

export const calculateCorrectWrongFromSubjects = (
	subjectDivision: Record<string, any>, 
	type: string, 
	category: 'type' | 'difficulty'
): { correct: number; wrong: number } => {
	let correct = 0;
	let wrong = 0;
	
	Object.values(subjectDivision || {}).forEach((subjectData: any) => {
		const categoryData = subjectData?.[category]?.[type];
		if (categoryData) {
			correct += categoryData.correct || 0;
			wrong += categoryData.wrong || 0;
		}
	});
	
	return { correct, wrong };
};

export const transformDataToAnalytics = (
	data: Record<string, number>, 
	subjectDivision: Record<string, any>, 
	category: 'type' | 'difficulty'
): AnalyticsData[] => {
	return Object.entries(data || {}).map(([key, value]) => {
		const { correct, wrong } = calculateCorrectWrongFromSubjects(subjectDivision, key, category);
		return {
			name: key,
			total: value,
			correct: { value: correct, fill: CHART_COLORS.correct },
			wrong: { value: wrong, fill: CHART_COLORS.wrong },
		};
	});
};

export const transformSubjectData = (data: Record<string, any>): AnalyticsData[] => {
	return Object.entries(data || {}).map(([key, value]: [string, any]) => ({
		name: key,
		total: (value.correct || 0) + (value.wrong || 0),
		correct: { value: value.correct || 0, fill: CHART_COLORS.correct },
		wrong: { value: value.wrong || 0, fill: CHART_COLORS.wrong },
	}));
};
