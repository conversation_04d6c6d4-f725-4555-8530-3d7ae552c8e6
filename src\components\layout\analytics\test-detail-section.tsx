import { Donut<PERSON>hart } from "@/components/charts";
import { CHART_COLORS } from "./types";
import { formatName } from "./utils";

interface TestDetailSectionProps {
	title: string;
	data: Record<string, number>;
	borderColor?: string;
	enableShowTotalCta?: boolean;
}

export const TestDetailSection = ({
	title,
	data,
	borderColor = "border-gray-200",
	enableShowTotalCta = false,
}: TestDetailSectionProps) => (
	<div className="flex flex-col">
		{enableShowTotalCta && (
			<button className="text-accent text-sm font-semibold underline underline-offset-[3px] hover:no-underline transition-all duration-200 w-fit mb-4">
				Show Total
			</button>
		)}
		<h3 className="text-sm font-semibold text-gray-600 mb-4">{title}</h3>
		<div className="space-y-2.5">
			{Object.entries(data).map(([key, total], index) => (
				<div
					key={index}
					className={`flex items-center justify-between py-2.5 px-5 bg-white border ${borderColor} rounded-lg shadow-sm`}
				>
					<div className="flex flex-col">
						<span className="text-sm font-semibold text-gray-600 mb-1">
							{formatName(key)}
						</span>
						<div className="flex items-baseline gap-1">
							<span className="text-3xl font-normal text-black">{total}</span>
							<span className="text-sm font-normal text-gray-400">total</span>
						</div>
					</div>
					<div className="flex-shrink-0">
						<DonutChart
							data={[
								{
									name: "Correct",
									value: Math.round(total * 0.75),
									fill: CHART_COLORS.correct,
								},
								{
									name: "Wrong",
									value: Math.round(total * 0.25),
									fill: CHART_COLORS.wrong,
								},
							]}
							showLegend={false}
							showTooltip={true}
							innerRadius={19}
							outerRadius={31}
							className="w-[63px] h-[63px]"
						/>
					</div>
				</div>
			))}
		</div>
	</div>
);
