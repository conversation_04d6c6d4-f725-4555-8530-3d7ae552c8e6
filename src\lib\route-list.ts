import {
	BookOpen,
	Clipboard,
	FileText,
	type Icon,
	LogOut,
	Paperclip,
	Zap,
	BarChart2,
	Book,
} from "react-feather";

type Submenu = {
	href: string;
	label: string;
	active?: boolean;
};

type Menu = {
	href: string;
	label: string;
	active?: boolean;
	icon: Icon;
	submenus?: Submenu[];
};

type Group = {
	groupLabel: string;
	menus: Menu[];
	exclude?: boolean;
	desktopOnly?: boolean;
};

export const routeList: Group[] = [
	{
		groupLabel: "",
		menus: [
			{
				href: "/dashboard",
				label: "Dashboard",
				icon: BookOpen,
				submenus: [],
			},
			{
				href: "/analytics",
				label: "Analytics",
				icon: BarChart2,
				submenus: [],
			},
			{
				href: "/learn",
				label: "Resources",
				icon: Book,
				submenus: [],
			},
		],
	},
	{
		groupLabel: "attempt tests",
		menus: [
			{
				href: "/t/mock",
				label: "Mock Test",
				icon: Clipboard,
				submenus: [],
			},
			{
				href: "/t/custom",
				label: "Custom Test",
				icon: Paperclip,
				submenus: [],
			},
			{
				href: "/t/subject",
				label: "Subject Test",
				icon: FileText,
				submenus: [],
			},
			{
				href: "/t/ai",
				label: "AI Based Test",
				icon: Zap,
				submenus: [],
			},
		],
	},
	{
		groupLabel: "",
		desktopOnly: true,
		menus: [
			{
				href: "",
				label: "Logout",
				icon: LogOut,
				submenus: [],
			},
		],
	},
	{
		groupLabel: "",
		exclude: true,
		menus: [
			{
				href: "/learn",
				label: "Learning Suggestions",
				icon: LogOut,
				submenus: [],
			},
		],
	},
	{
		groupLabel: "",
		exclude: true,
		menus: [
			{
				href: "/attempt",
				label: "Attempt",
				icon: Clipboard,
				submenus: [],
			},
		],
	},
];
