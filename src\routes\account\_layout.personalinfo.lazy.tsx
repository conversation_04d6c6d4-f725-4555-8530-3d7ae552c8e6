import { create<PERSON>azyFileRout<PERSON>, useRouter } from "@tanstack/react-router";
import { defineStepper } from "@stepperize/react";
import {
	educationalBackgroundValidation,
	EmptyValidation,
	emptyValidation,
	OnboardUserValidation,
	personalInfoValidation,
} from "@/features/auth/schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import React, { useRef } from "react";
import PersonalInfo from "@/components/forms/PersonalInfo";
import EducationalBackground from "@/components/forms/EducationalBackground";
import Complete from "@/components/forms/Complete";
import { ChevronsRight } from "react-feather";
import { useOnboardUser } from "@/lib/queries/user.query";
import { useUserStore } from "@/features/user/store";
import { useShallow } from "zustand/react/shallow";
import { useAuthStore } from "@/features/auth/store";
import { toast } from "@/hooks/use-toast";

const { useStepper, steps } = defineStepper(
	{
		id: "personalInfo",
		label: "Personal Info",
		schema: personalInfoValidation,
	},
	{
		id: "educationalBackground",
		label: "Educational Background",
		schema: educationalBackgroundValidation,
	},
	{ id: "trialTest", label: "Trial Test", schema: emptyValidation }
);

const Page = () => {
	const router = useRouter();
	const formRef = useRef<HTMLFormElement>(null);
	const { addInfoToTempUser, clearTempUserInfo, tempUserInfo } = useUserStore(
		useShallow((state) => ({
			addInfoToTempUser: state.addInfoToTempUser,
			clearTempUserInfo: state.clearTempUserInfo,
			tempUserInfo: state.tempUserInfo,
		}))
	);
	const {
		setLoggedIn,
		setIsNewUser,
		// setUser
	} = useAuthStore(
		useShallow((state) => ({
			setLoggedIn: state.setLoggedIn,
			setIsNewUser: state.setIsNewUser,
			setUser: state.setUser,
		}))
	);
	const { mutateAsync: onboardUser, isPending } = useOnboardUser();
	const stepper = useStepper();
	const form = useForm({
		mode: "onSubmit",
		resolver: zodResolver(stepper.current.schema),
		defaultValues: {
			phoneNumber: "",
			city: "",
			institute: "",
			educationBackground: "",
			currentClass: "",
			subjectGroup: [],
			targetEntryTests: [],
		},
	});

	const onSubmit = async (values: z.infer<typeof stepper.current.schema>) => {
		try {
			if (stepper.isLast) {
				if ((values as EmptyValidation).takeTrialTest) {
					setLoggedIn(true);
					router.navigate({ to: "/t/mock" });
				} else {
					setLoggedIn(true);
					router.invalidate();
				}
			} else {
				addInfoToTempUser(values);
				if (stepper.current.id === "educationalBackground") {
					const response = await onboardUser({
						...tempUserInfo,
						...values,
					} as OnboardUserValidation);
					if (response.data.success) {
						// setUser(response.data.data);
						setIsNewUser(false);
						stepper.next();
					}
					clearTempUserInfo();
				} else {
					stepper.next();
				}
			}
		} catch (error) {
			console.error("Error updating user:", error);
			toast({
				title: "Onboarding Failed",
				description:
					"There was a problem saving your profile information. Please try again.",
				variant: "destructive",
			});
		}
	};

	const goBack = () => {
		stepper.prev();
	};

	return (
		<Form {...form}>
			<form
				ref={formRef}
				onSubmit={form.handleSubmit(onSubmit)}
				className="space-y-6 rounded-lg flex flex-col-reverse lg:flex-col gap-y-4"
			>
				<div className="space-y-4">
					{stepper.switch({
						personalInfo: () => <PersonalInfo />,
						educationalBackground: () => (
							<EducationalBackground goBack={goBack} loading={isPending} />
						),
						trialTest: () => <Complete formRef={formRef} />,
					})}
				</div>
				<nav aria-label="Checkout Steps" className="group">
					<ol
						className="flex items-center justify-between sm:gap-4"
						aria-orientation="horizontal"
					>
						{stepper.all.map((step, index, array) => (
							<React.Fragment key={step.id}>
								<li className="flex flex-col md:flex-row items-center gap-2 flex-shrink-0">
									<Button
										type="button"
										role="tab"
										size={"icon"}
										variant={
											index <= stepper.current.index ? "default" : "outline"
										}
										aria-current={
											stepper.current.id === step.id ? "step" : undefined
										}
										aria-posinset={index + 1}
										aria-setsize={steps.length}
										aria-selected={stepper.current.id === step.id}
										className="flex size-5 sm:size-7 items-center border-primary justify-center rounded-full"
										// onClick={() => stepper.goTo(step.id)}
									>
										{index + 1}
									</Button>
									<span className="sm:text-sm text-xs font-medium text-center">
										{step.label.split(" ").map((word, index) => (
											<>
												<span key={index} className="capitalize">
													{word}
												</span>{" "}
												<br className="md:hidden" />
											</>
										))}
									</span>
								</li>
								{index < array.length - 1 && (
									<ChevronsRight className="size-4" />
								)}
							</React.Fragment>
						))}
					</ol>
				</nav>
			</form>
		</Form>
	);
};

export const Route = createLazyFileRoute("/account/_layout/personalinfo")({
	component: Page,
});
