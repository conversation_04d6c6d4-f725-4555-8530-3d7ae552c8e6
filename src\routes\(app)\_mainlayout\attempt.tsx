import React, { useEffect } from "react";
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import { useMediaQuery } from "react-responsive";
import { createFile<PERSON>oute, useRouter, <PERSON> } from "@tanstack/react-router";
import { Clipboard, List, Paperclip, Zap } from "react-feather";

// --- Test Card ---
interface TestCardProps {
	title: string;
	icon: React.ElementType;
	link?: string;
	disabled?: boolean;
}

const TestCard: React.FC<TestCardProps> = ({
	title,
	icon: Icon,
	link,
	disabled,
}) => {
	return (
		<div
			className={
				"bg-accent rounded-[20px] relative h-[100px] " +
				(disabled
					? "opacity-50 cursor-not-allowed"
					: "hover:opacity-90 transition-opacity")
			}
		>
			<Link
				to={link}
				className="h-full flex flex-col justify-between"
				disabled={disabled}
			>
				{/* Decorative shape image */}
				<img
					src="/assets/images/attempt-card/clips.svg"
					alt=""
					className="absolute top-0 right-0 pointer-events-none select-none"
				/>
				{/* Icon */}
				<div className="flex justify-end relative z-10 mr-4 mt-3.5">
					<Icon size={32} className="text-white" />
				</div>

				{/* Title */}
				<p className="text-white font-semibold text-[15px] mx-4 mb-3.5 relative z-10">
					{title}
				</p>
			</Link>
		</div>
	);
};

// --- Page ---
const Page = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });
	const router = useRouter();

	// Redirect desktop users
	useEffect(() => {
		if (isDesktop) {
			router.navigate({ to: "/dashboard" });
		}
	}, [isDesktop, router]);

	if (isDesktop) return null;

	const testOptions = [
		{ id: "mock-tests", title: "Mock Tests", icon: Clipboard, link: "/t/mock" },
		{ id: "subject-tests", title: "Subject Tests", icon: List, disabled: true },
		{
			id: "custom-tests",
			title: "Custom Tests",
			icon: Paperclip,
			disabled: true,
		},
		{
			id: "ai-generated-tests",
			title: "AI Generated Tests",
			icon: Zap,
			disabled: true,
		},
	];

	return (
		<>
			<Navbar />
			<MobileSwitcher />

			<main className="container mx-auto p-4 pb-20">
				{/* Test Buttons Section */}
				<div className="grid grid-cols-2 gap-4">
					{testOptions.map((test) => (
						<TestCard
							key={test.id}
							title={test.title}
							icon={test.icon}
							link={test.link}
							disabled={test.disabled}
						/>
					))}
				</div>
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/attempt")({
	beforeLoad: () => {},
	component: Page,
});
