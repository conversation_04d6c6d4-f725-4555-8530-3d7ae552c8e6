import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "react-feather";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { ICONS } from "@/lib/assets/images";
import MobileCreditCard from "./mobile-credit";

type MobileCheckoutPageProps = {
	planType?: "daily" | "subscription" | "custom";
	price?: number;
	days?: number;
	onBack: () => void;
};

const MobileCheckoutPage = ({
	planType = "subscription",
	price = 200,
	days,
	onBack,
}: MobileCheckoutPageProps) => {
	const [selectedPeriod, setSelectedPeriod] = useState("1 Month");
	const [showTransferDetails, setShowTransferDetails] = useState(false);
	const [showCreditCardPayment, setShowCreditCardPayment] = useState(false);
	const [paymentMethod, setPaymentMethod] = useState<string | null>(null);

	const isPeriodSelectionVisible = planType === "subscription";

	const handlePaymentMethodSelection = (method: string) => {
		setPaymentMethod(method);
		if (method === "transfer") {
			setShowTransferDetails(true);
			setShowCreditCardPayment(false);
		} else if (method === "credit-card") {
			setShowCreditCardPayment(true);
			setShowTransferDetails(false);
		}
	};

	if (showCreditCardPayment) {
		return (
			<MobileCreditCard
				price={price}
				period={selectedPeriod}
				onBack={() => setShowCreditCardPayment(false)}
				onProceed={() => {
					console.log("Proceeding with payment...");
				}}
			/>
		);
	}

	console.log(days, "_________days");

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col items-center p-4 space-y-16">
			<div className="w-full flex items-center justify-between  mt-10">
				<Button
					variant="ghost"
					size="sm"
					onClick={onBack}
					className="p-1 h-8 w-8"
				>
					<ArrowLeft className="h-5 w-5 text-gray-700" />
				</Button>
				<div className="flex-grow flex justify-center">
					<img src={ICONS.logoexpanded} alt="parhali" className="w-24" />
				</div>
				<div className="w-8"></div>
			</div>

			<Card className="w-full max-w-md bg-white shadow-md rounded-xl">
				<CardContent className="p-6 space-y-5">
					<div className="text-center mb-2">
						<h1 className="text-2xl font-bold">Your Cart</h1>
						<p className=" text-gray-600 mt-1">
							Please proceed with your payment gateway
						</p>
					</div>

					<div className="text-center mb-2">
						<span className="text-sm font-bold">Rs.</span>
						<span className="text-2xl font-bold"> {price}</span>
						<span className="text-xs text-gray-500 font-medium"> total</span>
					</div>

					{isPeriodSelectionVisible && (
						<div className="mb-4">
							<p className="text-xs font-medium text-gray-700 mb-1">
								Select Period
							</p>
							<Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
								<SelectTrigger className="w-full h-12 text-sm bg-white rounded-lg border-gray-200">
									<SelectValue placeholder="Select period" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="1 Month">1 Month</SelectItem>
									<SelectItem value="3 Months">3 Months</SelectItem>
									<SelectItem value="6 Months">6 Months</SelectItem>
									<SelectItem value="1 Year">1 Year</SelectItem>
								</SelectContent>
							</Select>
						</div>
					)}

					<div>
						<p className="text-xs font-medium text-gray-700 mb-4">
							Please select payment method:
						</p>

						{!showTransferDetails ? (
							<div className="grid grid-cols-2 gap-3">
								<Button
									variant="outline"
									className={`w-full rounded-lg border h-auto py-4 px-4 text-center justify-center whitespace-normal text-sm leading-snug ${
										paymentMethod === "credit-card"
											? "bg-accent text-white"
											: "bg-white"
									}`}
									onClick={() => handlePaymentMethodSelection("credit-card")}
								>
									Get instant access with Credit Card
								</Button>

								<Button
									variant="outline"
									className={`w-full rounded-lg h-auto py-4 px-4 text-center justify-center whitespace-normal text-sm leading-snug ${
										paymentMethod === "transfer"
											? "bg-accent text-white"
											: "bg-white"
									}`}
									onClick={() => handlePaymentMethodSelection("transfer")}
								>
									Transfer and send screenshot to WhatsApp
								</Button>
							</div>
						) : (
							<div className="space-y-4">
								<div className="grid grid-cols-2 gap-3">
									<Button
										variant="outline"
										className="w-full rounded-lg border h-auto py-4 px-4 text-center justify-center whitespace-normal text-sm leading-snug"
										onClick={() => handlePaymentMethodSelection("credit-card")}
									>
										Get instant access with Credit Card
									</Button>

									<Button
										variant="outline"
										className="w-full rounded-lg h-auto py-4 px-4 bg-accent text-white text-center justify-center whitespace-normal text-sm leading-snug"
										onClick={() => handlePaymentMethodSelection("transfer")}
									>
										Transfer and send screenshot to WhatsApp
									</Button>
								</div>

								<div className="p-3 border border-gray-200 rounded-lg">
									<p className="font-medium text-sm ">
										Please Send your payment to the following account:
									</p>
									<div className="space-y-1 text-sm">
										<p>Bank Name:</p>
										<p>Account Title: Hadi Khan</p>
										<p>Account #:</p>
										<p>IBAN:</p>
										<p className="text-success mt-2 text-sm">
											*Please send us proof of your payment along with your ID
											at XXXX XXXXXXX
										</p>
									</div>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default MobileCheckoutPage;
