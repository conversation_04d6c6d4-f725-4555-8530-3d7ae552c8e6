import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { createLazyFileRoute, useNavigate } from "@tanstack/react-router";
import { registerValidation, RegisterValidation } from "@/features/auth/schema";
import { ICONS } from "@/lib/assets/images";
import { useShallow } from "zustand/react/shallow";
import { useAuthStore } from "@/features/auth/store";
import {
	browserSessionPersistence,
	getAdditionalUserInfo,
	User,
} from "firebase/auth";
import { useCreateUser } from "@/lib/queries/auth.query";
import { formatErrorMsg } from "@/helpers/firebase";
import { useErrorToast } from "@/hooks/use-error-toast";
import auth from "@/lib/config/firebase";

const Login = () => {
	const showError = useErrorToast();
	const {
		mutateAsync: createUserQuery,
		isError,
		error,
		isPending,
	} = useCreateUser();
	const { register, loginWithGoogle, loading } = useAuthStore(
		useShallow((state) => ({
			register: state.register,
			loginWithGoogle: state.loginWithGoogle,
			loading: state.loading,
		}))
	);
	const navigate = useNavigate();
	const form = useForm<RegisterValidation>({
		resolver: zodResolver(registerValidation),
		defaultValues: {
			fullName: "",
			email: "",
			password: "",
			confirmPassword: "",
		},
	});

	const onSubmit = async (values: RegisterValidation) => {
		const result = await register(
			values.fullName,
			values.email,
			values.password
		);
		if (result instanceof Error) {
			showError(formatErrorMsg(result.message));
		} else {
			navigate({ to: "/account/verifyemail" });
			sendCreateUserToken();
		}
	};

	const registerWithGoogle = async () => {
		const result = await loginWithGoogle();
		if (result instanceof Error) {
			showError(formatErrorMsg(result.message));
		} else if (result) {
			const additionalInfo = getAdditionalUserInfo(result);
			if (additionalInfo?.isNewUser) {
				sendCreateUserToken();
			} else {
				checkOnboarded(result.user);
			}
		}
	};

	const checkOnboarded = async (user: User) => {
		const onboarded = (await user.getIdTokenResult()).claims["onboarded"];
		if (!onboarded) {
			navigate({ to: "/account/personalinfo" });
		} else {
			auth.setPersistence(browserSessionPersistence);
		}
	};

	const sendCreateUserToken = async () => {
		const response = await createUserQuery();
		if (response?.data.success) {
			navigate({ to: "/account/personalinfo" });
		} else if (isError) {
			showError(error.message);
		}
	};

	return (
		<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<h1>Register</h1>
			<p className="text-gray-600 text-center  max-w-[400px]">
				Join Parhlai to access expert curated test prep materials and boost your
				chances of success.
			</p>
			<div className="w-full flex flex-col gap-y-6">
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="gap-y-4 flex flex-col"
					>
						<FormField
							control={form.control}
							name="fullName"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Full Name
									</FormLabel>
									<FormControl>
										<Input placeholder="e.g. Saboor Ahmed" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Email
									</FormLabel>
									<FormControl>
										<Input
											placeholder="<EMAIL>"
											type="email"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Password
									</FormLabel>
									<FormControl>
										<PasswordInput
											placeholder="Enter your Password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="confirmPassword"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Confirm Password
									</FormLabel>
									<FormControl>
										<PasswordInput
											placeholder="Confirm your Password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<Button
							disabled={loading}
							type="submit"
							className="w-full"
							loading={loading || isPending}
						>
							Register
						</Button>
					</form>
				</Form>
				<div className="flex items-center">
					<hr className="flex-1 border-gray-300" />
					<p className="text-gray-400 px-2 text-sm">Or</p>
					<hr className="flex-1 border-gray-300" />
				</div>
				<div className="flex w-full gap-x-2">
					<Button
						variant="outline"
						className="flex-1 text-xs sm:text-sm"
						onClick={registerWithGoogle}
					>
						<img src={ICONS.google} alt="google" />
						Google
					</Button>
					{/* Facebook registration disabled - not configured yet */}
					{/* <Button
						variant="outline"
						className="flex-1 text-xs sm:text-sm"
						onClick={loginWithFacebook}
					>
						<img src={ICONS.facebook} alt="facebook" />
						Facebook
					</Button> */}
				</div>
				<p className="text-center shadcn text-sm">
					Already have an account?
					<span>
						<Button
							variant={"link"}
							className="px-0 pl-1"
							onClick={() => navigate({ to: "/account/login" })}
						>
							Login
						</Button>
					</span>
				</p>
			</div>
		</div>
	);
};

export const Route = createLazyFileRoute("/account/_layout/register")({
	component: Login,
});
